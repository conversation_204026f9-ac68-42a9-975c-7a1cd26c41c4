#ssh -fN -L 3306:rm-2zej253uuzl83d0xg.mysql.rds.aliyuncs.com:3306 root@************
#ssh -fN -L 6379:127.0.0.1:6379 root@************
---
spring:
  profiles: local
  application:
    name: fytHealthCloudService-dev
  jackson:
    default-property-inclusion: non_null
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************************************
    username: fyt_sql
    password: fyt_12345678
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      #初始化连接数量，最大最小连接数
      initialSize: 10
      maxActive: 500
      minIdle: 3
      #获取连接等待超时的时间
      maxWait: 600000
      #超过时间限制是否回收
      removeAbandoned: true
      #超过时间限制多长
      removeAbandonedTimeout: 180
      #配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 600000
      #配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      #用来检测连接是否有效的sql，要求是一个查询语句
      validationQuery: SELECT 1 FROM DUAL
      #申请连接的时候检测
      testWhileIdle: true
      #申请连接时执行validationQuery检测连接是否有效，配置为true会降低性能
      testOnBorrow: false
      #归还连接时执行validationQuery检测连接是否有效，配置为true会降低性能
      testOnReturn: false
      #打开PSCache，并且指定每个连接上PSCache的大小
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 50
      #属性类型是字符串，通过别名的方式配置扩展插件，常用的插件有：
      #监控统计用的filter:stat 日志用的filter:log4j 防御SQL注入的filter:wall
      filters: stat,wall,slf4j
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      useGlobalDataSourceStat: true

  redis:
    host: 127.0.0.1
    port: 6379
    timeout: 0
    pool:
      max-wait: -1
      max-active: 3000
      max-idle: 200
      min-idle: 0
    password: fyt_12345678
redis-custom:
  key:
    run-env: dev

server:
  port: 8080
  connection-timeout: 30000
  tomcat:
    min-spare-threads: 100
    max-threads: 10000
    max-connections: 20000
    uri-encoding: UTF-8
    accept-count: 1000
#  ssl:
#    key-store: classpath:3389896_sunnytechx.com.pfx
#    key-store-password: D7eAxVas
#    key-store-type: PKCS12

logging:
  config: classpath:log4j.xml
mybatis:
  mapper-locations: classpath:mapper/*.xml
  # spring boot集成mybatis的方式打印sql
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
task:
  pool:
    corePoolSize: 10
    maxPoolSize: 50
    queueCapacity: 200
export:
  path: /Downloads/reportFrom
swagger:
  enabled: true

# JWT配置
jwt:
  secret: fytHealthCloudServiceJwtSecretKeyForDevelopment2023
  expiration: 86400000  # 24小时，单位：毫秒

---
spring:
  profiles: dev
  application:
    name: fytHealthCloudService-dev
  jackson:
    default-property-inclusion: non_null
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************************************************************************
    username: fyt_sql
    password: fyt_12345678
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      #初始化连接数量，最大最小连接数
      initialSize: 10
      maxActive: 500
      minIdle: 3
      #获取连接等待超时的时间
      maxWait: 600000
      #超过时间限制是否回收
      removeAbandoned: true
      #超过时间限制多长
      removeAbandonedTimeout: 180
      #配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 600000
      #配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      #用来检测连接是否有效的sql，要求是一个查询语句
      validationQuery: SELECT 1 FROM DUAL
      #申请连接的时候检测
      testWhileIdle: true
      #申请连接时执行validationQuery检测连接是否有效，配置为true会降低性能
      testOnBorrow: false
      #归还连接时执行validationQuery检测连接是否有效，配置为true会降低性能
      testOnReturn: false
      #打开PSCache，并且指定每个连接上PSCache的大小
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 50
      #属性类型是字符串，通过别名的方式配置扩展插件，常用的插件有：
      #监控统计用的filter:stat 日志用的filter:log4j 防御SQL注入的filter:wall
      filters: stat,wall,slf4j
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      useGlobalDataSourceStat: true

  redis:
    host: 127.0.0.1
    port: 6379
    timeout: 0
    pool:
      max-wait: -1
      max-active: 3000
      max-idle: 200
      min-idle: 0
    password: fyt_12345678

redis-custom:
  key:
    run-env: dev

server:
  port: 8080
  connection-timeout: 30000
  tomcat:
    min-spare-threads: 100
    max-threads: 10000
    max-connections: 20000
    uri-encoding: UTF-8
    accept-count: 1000
  ssl:
    key-store: classpath:healthdevapi.fytkj.com.cn.pfx
    key-store-password: uvcgd844
    key-store-type: PKCS12

logging:
  config: classpath:log4j.xml
mybatis:
  mapper-locations: classpath:mapper/*.xml
task:
  pool:
    corePoolSize: 10
    maxPoolSize: 50
    queueCapacity: 200
export:
  path: /Downloads/reportFrom
swagger:
  enabled: true

# JWT配置
jwt:
  secret: fytHealthCloudServiceJwtSecretKeyForDevelopment2023
  expiration: 86400000  # 24小时，单位：毫秒

---
spring:
  profiles: prod
  application:
    name: fytHealthCloudService-prod
  jackson:
    default-property-inclusion: non_null

  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************************************************************************
    username: fyt_sql
    password: fyt_12345678
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      #初始化连接数量，最大最小连接数
      initialSize: 10
      maxActive: 500
      minIdle: 3
      #获取连接等待超时的时间
      maxWait: 600000
      #超过时间限制是否回收
      removeAbandoned: true
      #超过时间限制多长
      removeAbandonedTimeout: 180
      #配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 600000
      #配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      #用来检测连接是否有效的sql，要求是一个查询语句
      validationQuery: SELECT 1 FROM DUAL
      #申请连接的时候检测
      testWhileIdle: true
      #申请连接时执行validationQuery检测连接是否有效，配置为true会降低性能
      testOnBorrow: false
      #归还连接时执行validationQuery检测连接是否有效，配置为true会降低性能
      testOnReturn: false
      #打开PSCache，并且指定每个连接上PSCache的大小
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 50
      #属性类型是字符串，通过别名的方式配置扩展插件，常用的插件有：
      #监控统计用的filter:stat 日志用的filter:log4j 防御SQL注入的filter:wall
      filters: stat,wall,slf4j
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      useGlobalDataSourceStat: true

  redis:
    host: 127.0.0.1
    port: 6379
    timeout: 0
    pool:
      max-wait: -1
      max-active: 3000
      max-idle: 200
      min-idle: 0
    password: fyt_12345678

redis-custom:
  key:
    run-env: prod
server:
  port: 9080
  connection-timeout: 30000
  tomcat:
    min-spare-threads: 100
    max-threads: 10000
    max-connections: 20000
    uri-encoding: UTF-8
    accept-count: 1000
#  ssl:
#    key-store: classpath:apiweigh.dianqintech.com.pfx
#    key-store-password: r6fdwp13
#    key-store-type: PKCS12

logging:
  config: classpath:log4j.xml
mybatis:
  mapper-locations: classpath:mapper/*.xml
task:
  pool:
    corePoolSize: 10
    maxPoolSize: 50
    queueCapacity: 400

export:
  path: /Downloads/reportFrom

swagger:
  enabled: false

# JWT配置
jwt:
  secret: fytHealthCloudServiceJwtSecretKeyForProduction2023SecureKey
  expiration: 86400000  # 24小时，单位：毫秒
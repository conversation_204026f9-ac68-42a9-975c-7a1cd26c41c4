package com.fyt.health.cloud.service.user.dto;

import com.fyt.health.cloud.service.user.enums.UserStatus;
import com.fyt.health.cloud.service.user.enums.UserType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "用户查询请求")
public class UserQueryRequest {
    
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long id;
    
    /**
     * 用户类型
     */
    @ApiModelProperty(value = "用户类型")
    private UserType userType;
    
    /**
     * 用户状态
     */
    @ApiModelProperty(value = "用户状态")
    private UserStatus status;
    
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phoneNumber;
    
    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCard;
    
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名，支持模糊查询")
    private String realName;
    
    /**
     * 当前用户ID
     */
    @ApiModelProperty(value = "当前用户ID", hidden = true)
    private Long currentUserId;
    
    /**
     * 当前用户类型
     */
    @ApiModelProperty(value = "当前用户类型", hidden = true)
    private UserType currentUserType;
    
    /**
     * 代理商ID
     */
    @ApiModelProperty(value = "代理商ID")
    private Long agentId;
    
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码，默认为1")
    private Integer pageNum = 1;
    
    /**
     * 每页记录数
     */
    @ApiModelProperty(value = "每页记录数，默认为10")
    private Integer pageSize = 10;

    /**
     * 计算偏移量
     * @return 偏移量
     */
    public Integer getOffset() {
        if (pageNum == null || pageSize == null) {
            return 0;
        }
        return (pageNum - 1) * pageSize;
    }
} 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fyt.health.cloud.service.user.dao.UserMapper">

    <resultMap id="BaseResultMap" type="com.fyt.health.cloud.service.user.entity.User">
        <id column="id" property="id"/>
        <result column="user_type" property="userType"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="real_name" property="realName"/>
        <result column="id_card" property="idCard"/>
        <result column="gender" property="gender"/>
        <result column="age" property="age"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="address" property="address"/>
        <result column="guardian_phone" property="guardianPhone"/>
        <result column="guardian_name" property="guardianName"/>
        <result column="guardian_gender" property="guardianGender"/>
        <result column="guardian_relation" property="guardianRelation"/>
        <result column="agent_id" property="agentId"/>
        <result column="registration_time" property="registrationTime"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="status" property="status"/>
        <result column="invite_code" property="inviteCode"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_type, username, password, real_name, id_card, gender, age, phone_number,
        address, guardian_phone, guardian_name, guardian_gender, guardian_relation, agent_id, registration_time,
        last_login_time, status, invite_code, created_at, updated_at, deleted
    </sql>

    <insert id="insert" parameterType="com.fyt.health.cloud.service.user.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (
            user_type, username, password, real_name, id_card, gender, age, phone_number,
            address, guardian_phone, guardian_name, guardian_gender, guardian_relation, agent_id, registration_time,
            status, invite_code, deleted
        ) VALUES (
            #{userType}, #{username}, #{password}, #{realName}, #{idCard}, #{gender}, #{age}, #{phoneNumber},
            #{address}, #{guardianPhone}, #{guardianName}, #{guardianGender}, #{guardianRelation}, #{agentId}, #{registrationTime},
            #{status}, #{inviteCode}, 1
        )
    </insert>

    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE id = #{id} AND deleted = 1
    </select>

    <select id="findByPhoneNumber" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE phone_number = #{phoneNumber} AND deleted = 1
    </select>

    <select id="findByIdCard" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE id_card = #{idCard} AND deleted = 1
    </select>

    <update id="updateLastLoginTime">
        UPDATE users
        SET last_login_time = #{lastLoginTime},
            updated_at = NOW()
        WHERE id = #{id} AND deleted = 1
    </update>

    <select id="findByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        <where>
            deleted = 1
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="userType != null">
                AND user_type = #{userType}
            </if>
            <if test="status != null">
                AND status = #{status.code}
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                AND phone_number = #{phoneNumber}
            </if>
            <if test="idCard != null and idCard != ''">
                AND id_card = #{idCard}
            </if>
            <if test="realName != null and realName != ''">
                AND real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <!-- 如果当前用户是代理商，只能查看自己的用户 -->
            <if test="currentUserType != null and currentUserType.name() == 'AGENT'">
                AND agent_id = #{currentUserId}
            </if>
            <!-- 如果指定了代理商ID，只查看该代理商的用户 -->
            <if test="agentId != null">
                AND agent_id = #{agentId}
            </if>
        </where>
        ORDER BY id DESC
        <if test="pageNum != null and pageSize != null">
            LIMIT #{pageSize} OFFSET #{offset}
        </if>
    </select>

    <select id="countByCondition" resultType="long">
        SELECT COUNT(*)
        FROM users
        <where>
            deleted = 1
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="userType != null">
                AND user_type = #{userType}
            </if>
            <if test="status != null">
                AND status = #{status.code}
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                AND phone_number = #{phoneNumber}
            </if>
            <if test="idCard != null and idCard != ''">
                AND id_card = #{idCard}
            </if>
            <if test="realName != null and realName != ''">
                AND real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <!-- 如果当前用户是代理商，只能查看自己的用户 -->
            <if test="currentUserType != null and currentUserType.name() == 'AGENT'">
                AND agent_id = #{currentUserId}
            </if>
            <!-- 如果指定了代理商ID，只查看该代理商的用户 -->
            <if test="agentId != null">
                AND agent_id = #{agentId}
            </if>
        </where>
    </select>

    <update id="updateStatus">
        UPDATE users
        SET status = #{status},
            updated_at = NOW()
        WHERE id = #{id} AND deleted = 1
    </update>

    <update id="deleteLogically">
        UPDATE users
        SET deleted = 0,
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <select id="getAgentNameById" resultType="string">
        SELECT real_name
        FROM users
        WHERE id = #{agentId} AND user_type = 'AGENT' AND deleted = 1
    </select>
</mapper>
package com.fyt.health.cloud.service.user.service.impl;

import com.fyt.health.cloud.service.common.entity.BaseResponse;
import com.fyt.health.cloud.service.constant.BaseResponseConstant;
import com.fyt.health.cloud.service.redis.RedisHandler;
import com.fyt.health.cloud.service.user.dao.UserMapper;
import com.fyt.health.cloud.service.user.dto.*;
import com.fyt.health.cloud.service.user.entity.User;
import com.fyt.health.cloud.service.user.enums.UserStatus;
import com.fyt.health.cloud.service.user.enums.UserType;
import com.fyt.health.cloud.service.user.exception.UserException;
import com.fyt.health.cloud.service.user.service.UserService;
import com.fyt.health.cloud.service.user.utils.JwtUtil;
import com.fyt.health.cloud.service.user.utils.PasswordUtil;
import com.fyt.health.cloud.service.user.utils.UserRedisKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private PasswordUtil passwordUtil;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private RedisHandler redisHandler;
    
    @Value("${login.max-fail-count:5}")
    private Integer maxLoginFailCount;
    
    @Value("${login.lock-time-minutes:30}")
    private Integer lockTimeMinutes;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse<UserInfoResponse> register(UserRegisterRequest request) {
        // 校验手机号是否已存在
        User existUser = userMapper.findByPhoneNumber(request.getPhoneNumber());
        if (existUser != null) {
            throw UserException.userAlreadyExists();
        }
        
        // 校验身份证号是否已存在
        existUser = userMapper.findByIdCard(request.getIdCard());
        if (existUser != null) {
            throw UserException.invalidParameter("身份证号已存在");
        }
        
        // 如果是普通用户，验证监护人信息
        if (UserType.USER.equals(request.getUserType())) {
            if (request.getGuardianPhone() == null || request.getGuardianName() == null) {
                throw UserException.invalidParameter("普通用户必须填写监护人信息");
            }
        }
        
        // 如果是代理商，需要填写邀请码（可以根据实际需求调整）
        if (UserType.AGENT.equals(request.getUserType()) && request.getInviteCode() == null) {
            throw UserException.invalidParameter("代理商注册需要填写邀请码");
        }
        
        // 创建用户
        User user = new User();
        BeanUtils.copyProperties(request, user);
        // 根据身份证号计算年龄
        if (user.getIdCard() != null && !user.getIdCard().isEmpty()) {
            user.setAge(calculateAgeFromIdCard(user.getIdCard()));
        }
        user.setPassword(passwordUtil.encode(request.getPassword())); // 密码加密
        user.setRegistrationTime(LocalDateTime.now());
        user.setStatus(UserStatus.NORMAL.getCode());
        user.setDeleted(1); // 1: 未删除
        
        // 保存用户
        userMapper.insert(user);
        
        // 返回用户信息
        UserInfoResponse response = convertToUserInfoResponse(user);
        return BaseResponse.<UserInfoResponse>builder()
                .state(BaseResponseConstant.STATE_OK)
                .message(BaseResponseConstant.MESSAGE_OK)
                .data(response)
                .build();
    }
    
    @Override
    public BaseResponse<UserLoginResponse> login(UserLoginRequest request) {
        // 检查用户是否被锁定
        String lockKey = UserRedisKey.getUserLockKey(request.getPhoneNumber());
        String lockValue = redisHandler.getHashValueFromRedis(lockKey, "locked");
        if ("1".equals(lockValue)) {
            throw UserException.accountLocked();
        }
        
        // 查询用户
        User user = userMapper.findByPhoneNumber(request.getPhoneNumber());
        if (user == null) {
            // 记录登录失败次数
            recordLoginFail(request.getPhoneNumber());
            throw UserException.invalidCredentials();
        }
        
        // 检查用户状态
        if (UserStatus.DISABLED.getCode() == (user.getStatus())) {
            throw UserException.accountDisabled();
        }
        
        // 验证密码
        if (!passwordUtil.matches(request.getPassword(), user.getPassword())) {
            // 记录登录失败次数
            recordLoginFail(request.getPhoneNumber());
            throw UserException.invalidCredentials();
        }
        
        // 更新最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        userMapper.updateLastLoginTime(user.getId(), user.getLastLoginTime());
        
        // 清除登录失败记录
        clearLoginFail(request.getPhoneNumber());
        
        // 生成Token
        String token = jwtUtil.generateToken(user.getId(), user.getUserType());
        
        // 将Token存入Redis
        redisHandler.putUserToRedis(
                UserRedisKey.getUserTokenKey(user.getId()),
                token,
                24 * 60 * 60, // 24小时
                TimeUnit.SECONDS
        );
        
        // 构建响应
        UserInfoResponse userInfoResponse = convertToUserInfoResponse(user);
        UserLoginResponse loginResponse = UserLoginResponse.builder()
                .token(token)
                .userInfo(userInfoResponse)
                .build();
        
        return BaseResponse.<UserLoginResponse>builder()
                .state(BaseResponseConstant.STATE_OK)
                .message(BaseResponseConstant.MESSAGE_OK)
                .data(loginResponse)
                .build();
    }
    
    @Override
    public BaseResponse<Void> logout(Long userId, String token) {
        // 将Token加入黑名单
        if (token != null) {
            redisHandler.putUserToRedis(
                    UserRedisKey.getTokenBlacklistKey(token),
                    "1",
                    24 * 60 * 60, // 24小时
                    TimeUnit.SECONDS
            );
        }
        
        // 删除用户Token
        redisHandler.deleteUser(UserRedisKey.getUserTokenKey(userId));
        
        return BaseResponse.<Void>builder()
                .state(BaseResponseConstant.STATE_OK)
                .message(BaseResponseConstant.MESSAGE_OK)
                .build();
    }
    
    @Override
    public BaseResponse<UserInfoResponse> getUserInfo(Long id, Long currentUserId, String currentUserType) {
        User user = userMapper.findById(id);
        if (user == null) {
            throw UserException.userNotFound();
        }
        
        // 检查权限
        checkPermission(id, currentUserId, UserType.valueOf(currentUserType));
        
        UserInfoResponse response = convertToUserInfoResponse(user);
        return BaseResponse.<UserInfoResponse>builder()
                .state(BaseResponseConstant.STATE_OK)
                .message(BaseResponseConstant.MESSAGE_OK)
                .data(response)
                .build();
    }
    
    @Override
    public BaseResponse<PageResult<UserInfoResponse>> queryUserList(UserQueryRequest request) {
        // 查询总数
        long total = userMapper.countByCondition(request);
        
        List<UserInfoResponse> userList = new ArrayList<>();
        if (total > 0) {
            // 查询数据
            List<User> users = userMapper.findByCondition(request);
            // 转换数据
            for (User user : users) {
                userList.add(convertToUserInfoResponse(user));
            }
        }
        
        // 构建分页结果
        PageResult<UserInfoResponse> pageResult = PageResult.<UserInfoResponse>builder()
                .pageNum(request.getPageNum())
                .pageSize(request.getPageSize())
                .total(total)
                .list(userList)
                .build();
        pageResult.calcPages();
        
        return BaseResponse.<PageResult<UserInfoResponse>>builder()
                .state(BaseResponseConstant.STATE_OK)
                .message(BaseResponseConstant.MESSAGE_OK)
                .data(pageResult)
                .build();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse<Void> disableUser(Long id, Long currentUserId, String currentUserType) {
        User user = userMapper.findById(id);
        if (user == null) {
            throw UserException.userNotFound();
        }
        
        // 检查权限
        checkPermission(id, currentUserId, UserType.valueOf(currentUserType));
        
        // 禁用用户
        userMapper.updateStatus(id, UserStatus.DISABLED.getCode());
        
        return BaseResponse.<Void>builder()
                .state(BaseResponseConstant.STATE_OK)
                .message(BaseResponseConstant.MESSAGE_OK)
                .build();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse<Void> enableUser(Long id, Long currentUserId, String currentUserType) {
        User user = userMapper.findById(id);
        if (user == null) {
            throw UserException.userNotFound();
        }
        
        // 检查权限
        checkPermission(id, currentUserId, UserType.valueOf(currentUserType));
        
        // 启用用户
        userMapper.updateStatus(id, UserStatus.NORMAL.getCode());
        
        return BaseResponse.<Void>builder()
                .state(BaseResponseConstant.STATE_OK)
                .message(BaseResponseConstant.MESSAGE_OK)
                .build();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse<Void> deleteUser(Long id, Long currentUserId, String currentUserType) {
        User user = userMapper.findById(id);
        if (user == null) {
            throw UserException.userNotFound();
        }
        
        // 检查权限
        checkPermission(id, currentUserId, UserType.valueOf(currentUserType));
        
        // 逻辑删除用户
        userMapper.deleteLogically(id);
        
        return BaseResponse.<Void>builder()
                .state(BaseResponseConstant.STATE_OK)
                .message(BaseResponseConstant.MESSAGE_OK)
                .build();
    }
    
    /**
     * 将用户实体转换为用户信息响应DTO
     *
     * @param user 用户实体
     * @return 用户信息响应DTO
     */
    private UserInfoResponse convertToUserInfoResponse(User user) {
        UserInfoResponse response = new UserInfoResponse();
        BeanUtils.copyProperties(user, response);
        
        // 设置用户状态
        response.setStatus(UserStatus.fromCode(user.getStatus()));
        
        // 设置代理商名称
        if (user.getAgentId() != null) {
            String agentName = userMapper.getAgentNameById(user.getAgentId());
            response.setAgentName(agentName);
        }
        
        // 脱敏处理
        response.setIdCard(maskIdCard(user.getIdCard()));
        response.setPhoneNumber(maskPhoneNumber(user.getPhoneNumber()));
        if (user.getGuardianPhone() != null) {
            response.setGuardianPhone(maskPhoneNumber(user.getGuardianPhone()));
        }
        
        return response;
    }
    
    /**
     * 检查权限
     *
     * @param targetUserId 目标用户ID
     * @param currentUserId 当前用户ID
     * @param currentUserType 当前用户类型
     */
    private void checkPermission(Long targetUserId, Long currentUserId, UserType currentUserType) {
        // 管理员可以查看所有用户
        if (UserType.ADMIN.equals(currentUserType)) {
            return;
        }
        
        // 代理商只能查看自己的用户
        if (UserType.AGENT.equals(currentUserType)) {
            User targetUser = userMapper.findById(targetUserId);
            if (targetUser == null || !Objects.equals(targetUser.getAgentId(), currentUserId)) {
                throw UserException.unauthorized();
            }
        }
        
        // 普通用户只能查看自己
        if (UserType.USER.equals(currentUserType) && !Objects.equals(targetUserId, currentUserId)) {
            throw UserException.unauthorized();
        }
    }
    
    /**
     * 记录登录失败次数
     *
     * @param phoneNumber 手机号
     */
    private void recordLoginFail(String phoneNumber) {
        String failCountKey = UserRedisKey.getLoginFailCountKey(phoneNumber);
        String failCount = redisHandler.getHashValueFromRedis(failCountKey, "count");
        
        int count = 1;
        if (failCount != null && !failCount.isEmpty()) {
            count = Integer.parseInt(failCount) + 1;
        }
        
        redisHandler.putHashValueToRedis(failCountKey, "count", String.valueOf(count));
        
        // 如果失败次数达到上限，锁定账号
        if (count >= maxLoginFailCount) {
            String lockKey = UserRedisKey.getUserLockKey(phoneNumber);
            redisHandler.putHashValueToRedis(lockKey, "locked", "1");
            // 设置锁定时间
            redisHandler.putUserToRedis(lockKey, "1", lockTimeMinutes * 60, TimeUnit.SECONDS);
        }
    }
    
    /**
     * 清除登录失败记录
     *
     * @param phoneNumber 手机号
     */
    private void clearLoginFail(String phoneNumber) {
        String failCountKey = UserRedisKey.getLoginFailCountKey(phoneNumber);
        redisHandler.deleteHashValue(failCountKey, "count");
        
        String lockKey = UserRedisKey.getUserLockKey(phoneNumber);
        redisHandler.deleteHashValue(lockKey, "locked");
    }
    
    /**
     * 手机号脱敏
     *
     * @param phoneNumber 手机号
     * @return 脱敏后的手机号
     */
    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() != 11) {
            return phoneNumber;
        }
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(7);
    }
    
    /**
     * 身份证号脱敏
     *
     * @param idCard 身份证号
     * @return 脱敏后的身份证号
     */
    private String maskIdCard(String idCard) {
        if (idCard == null || idCard.length() < 8) {
            return idCard;
        }
        return idCard.substring(0, 4) + "**********" + idCard.substring(idCard.length() - 4);
    }

    /**
     * 根据身份证号计算年龄
     *
     * @param idCard 身份证号
     * @return 年龄
     */
    private Integer calculateAgeFromIdCard(String idCard) {
        if (idCard == null || idCard.length() < 14) {
            return null;
        }

        try {
            String birthDateStr;
            if (idCard.length() == 15) {
                // 15位身份证号，出生年份为19XX年
                birthDateStr = "19" + idCard.substring(6, 12);
            } else if (idCard.length() == 18) {
                // 18位身份证号
                birthDateStr = idCard.substring(6, 14);
            } else {
                return null;
            }

            // 解析出生日期
            int year = Integer.parseInt(birthDateStr.substring(0, 4));
            int month = Integer.parseInt(birthDateStr.substring(4, 6));
            int day = Integer.parseInt(birthDateStr.substring(6, 8));

            LocalDate birthDate = LocalDate.of(year, month, day);
            LocalDate currentDate = LocalDate.now();

            // 计算年龄
            Period period = Period.between(birthDate, currentDate);
            return period.getYears();
        } catch (Exception e) {
            log.warn("解析身份证号失败: {}", idCard, e);
            return null;
        }
    }
}
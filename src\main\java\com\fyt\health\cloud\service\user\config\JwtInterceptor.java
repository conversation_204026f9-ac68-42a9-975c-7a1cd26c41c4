package com.fyt.health.cloud.service.user.config;

import com.fyt.health.cloud.service.redis.RedisHandler;
import com.fyt.health.cloud.service.user.utils.JwtUtil;
import com.fyt.health.cloud.service.user.utils.UserRedisKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT拦截器
 */
@Slf4j
@Component
public class JwtInterceptor implements HandlerInterceptor {
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private RedisHandler redisHandler;

    private static final String AUTH_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {
        // 放行OPTIONS请求
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }
        
        // 获取Authorization头
        String authHeader = request.getHeader(AUTH_HEADER);
        if (authHeader == null || !authHeader.startsWith(BEARER_PREFIX)) {
            responseError(response, "未授权，请登录");
            return false;
        }
        
        // 获取Token
        String token = authHeader.substring(BEARER_PREFIX.length());
        
        try {
            // 检查Token是否在黑名单中
            String blacklistValue = redisHandler.getHashValueFromRedis(UserRedisKey.getTokenBlacklistKey(token), "blacklist");
            if ("1".equals(blacklistValue)) {
                responseError(response, "令牌已失效，请重新登录");
                return false;
            }
            
            // 获取用户ID和用户类型
            Long userId = jwtUtil.getUserIdFromToken(token);
            String userType = jwtUtil.getUserTypeFromToken(token).name();
            
            // 验证Token有效性
            if (!jwtUtil.validateToken(token, userId)) {
                responseError(response, "令牌已过期，请重新登录");
                return false;
            }
            
            // 将用户ID和用户类型添加到请求属性中，供Controller使用
            request.setAttribute("currentUserId", userId);
            request.setAttribute("currentUserType", userType);

            // 也添加到请求头中，以兼容不同的获取方式
            request.setAttribute("currentUserIdHeader", String.valueOf(userId));
            request.setAttribute("currentUserTypeHeader", userType);
            
            return true;
        } catch (Exception e) {
            log.error("JWT验证失败：", e);
            responseError(response, "令牌验证失败，请重新登录");
            return false;
        }
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }

    /**
     * 响应错误信息
     *
     * @param response 响应对象
     * @param errorMsg 错误信息
     * @throws IOException IO异常
     */
    private void responseError(HttpServletResponse response, String errorMsg) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"state\":0,\"message\":\"" + errorMsg + "\"}");
    }
} 
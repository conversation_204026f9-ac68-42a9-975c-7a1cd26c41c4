package com.fyt.health.cloud.service.user.utils;

import com.fyt.health.cloud.service.user.enums.UserType;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * JWT工具类
 */
@Component
public class JwtUtil {

    @Value("${jwt.secret:fytHealthCloudServiceSecretKey}")
    private String secretString;

    @Value("${jwt.expiration:3600000}")
    private Long expiration;

    private SecretKey secretKey;

    private static final String CLAIM_KEY_USER_ID = "userId";
    private static final String CLAIM_KEY_USER_TYPE = "userType";
    private static final String CLAIM_KEY_CREATED = "created";

    @PostConstruct
    public void init() {
        // 使用固定的密钥字符串，确保服务重启后token仍然有效
        // 如果配置的密钥长度不够，则扩展它
        String key = secretString;
        while (key.length() < 64) { // HS512需要至少64字节的密钥
            key += secretString;
        }
        secretKey = Keys.hmacShaKeyFor(key.substring(0, 64).getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 从token中获取用户名
     *
     * @param token token
     * @return 用户名
     */
    public Long getUserIdFromToken(String token) {
        return Long.valueOf(getClaimFromToken(token, claims -> claims.get(CLAIM_KEY_USER_ID).toString()));
    }

    /**
     * 从token中获取用户类型
     *
     * @param token token
     * @return 用户类型
     */
    public UserType getUserTypeFromToken(String token) {
        return UserType.valueOf(getClaimFromToken(token, claims -> claims.get(CLAIM_KEY_USER_TYPE).toString()));
    }

    /**
     * 从token中获取JWT的过期时间
     *
     * @param token token
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    /**
     * 从token中获取JWT中的声明
     *
     * @param token          token
     * @param claimsResolver 声明解析器
     * @return 声明
     */
    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }

    /**
     * 解析token获取所有声明
     *
     * @param token token
     * @return 所有声明
     */
    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(secretKey)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 检查token是否已过期
     *
     * @param token token
     * @return 是否已过期
     */
    private Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }

    /**
     * 生成token
     *
     * @param userId   用户ID
     * @param userType 用户类型
     * @return token
     */
    public String generateToken(Long userId, UserType userType) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_KEY_USER_ID, userId);
        claims.put(CLAIM_KEY_USER_TYPE, userType);
        claims.put(CLAIM_KEY_CREATED, new Date());
        return generateToken(claims);
    }

    /**
     * 根据声明生成JWT token
     *
     * @param claims 声明
     * @return token
     */
    private String generateToken(Map<String, Object> claims) {
        return Jwts.builder()
                .setClaims(claims)
                .setExpiration(generateExpirationDate())
                .signWith(secretKey, SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 生成token的过期时间
     *
     * @return 过期时间
     */
    private Date generateExpirationDate() {
        return new Date(System.currentTimeMillis() + expiration);
    }

    /**
     * 验证token是否有效
     *
     * @param token  token
     * @param userId 用户ID
     * @return 是否有效
     */
    public Boolean validateToken(String token, Long userId) {
        final Long tokenUserId = getUserIdFromToken(token);
        return (tokenUserId.equals(userId) && !isTokenExpired(token));
    }
} 
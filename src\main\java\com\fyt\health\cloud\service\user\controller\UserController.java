package com.fyt.health.cloud.service.user.controller;

import com.fyt.health.cloud.service.common.entity.BaseResponse;
import com.fyt.health.cloud.service.constant.BaseResponseConstant;
import com.fyt.health.cloud.service.user.dto.*;
import com.fyt.health.cloud.service.user.enums.UserType;
import com.fyt.health.cloud.service.user.service.UserService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户控制器
 */
@Api(value = "UserController", tags = "用户管理接口")
@RestController
@RequestMapping("/api/users")
public class UserController {

    private static final Logger log = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    /**
     * 用户注册
     *
     * @param request 注册请求
     * @return 注册结果
     */
    @ApiOperation(value = "用户注册", notes = "注册新用户，不同类型用户需要填写不同的信息：普通用户需要填写监护人信息，代理商需要填写邀请码", response = UserInfoResponse.class)
    @PostMapping("/register")
    public BaseResponse<UserInfoResponse> register(@RequestBody @Validated UserRegisterRequest request) {
        return userService.register(request);
    }

    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 登录结果
     */
    @ApiOperation(value = "用户登录", notes = "通过手机号和密码登录系统", response = UserLoginResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "登录成功", examples = @Example(
                    value = @ExampleProperty(
                            mediaType = "application/json",
                            value = "{\"state\":1,\"message\":\"成功\",\"data\":{\"token\":\"eyJhbGciOiJIUzI1NiJ9...\",\"userInfo\":{\"id\":1001,\"username\":\"zhangsan\"}}}"
                    )
            )),
            @ApiResponse(code = 400, message = "登录失败", examples = @Example(
                    value = @ExampleProperty(
                            mediaType = "application/json",
                            value = "{\"state\":0,\"message\":\"手机号或密码错误\"}"
                    )
            ))
    })
    @PostMapping("/login")
    public BaseResponse<UserLoginResponse> login(
            @ApiParam(value = "登录请求", required = true, examples = @Example(
                    value = @ExampleProperty(
                            mediaType = "application/json",
                            value = "{\"phoneNumber\":\"13611008912\",\"password\":\"12345678\"}"
                    )
            ))
            @RequestBody @Validated UserLoginRequest request) {
        return userService.login(request);
    }

    /**
     * 用户登出
     *
     * @param request HTTP请求，用于获取Authorization头
     * @param userId 用户ID
     * @return 登出结果
     */
    @ApiOperation(value = "用户登出", notes = "用户登出系统，需要提供Authorization头（Bearer token）和用户ID")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "登出成功", examples = @Example(
            value = @ExampleProperty(
                mediaType = "application/json",
                value = "{\"state\":1,\"message\":\"登出成功\"}"
            )
        )),
        @ApiResponse(code = 401, message = "未授权", examples = @Example(
            value = @ExampleProperty(
                mediaType = "application/json",
                value = "{\"state\":0,\"message\":\"未授权，请登录\"}"
            )
        ))
    })
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "Bearer token", required = true, dataType = "String", paramType = "header", example = "Bearer eyJhbGciOiJIUzUxMiJ9..."),
        @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "query", example = "1")
    })
    @PostMapping("/logout")
    public BaseResponse<Void> logout(HttpServletRequest request, @RequestParam Long userId) {
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            return BaseResponse.<Void>builder()
                    .state(BaseResponseConstant.STATE_ERROR)
                    .message("未授权，请登录")
                    .build();
        }
        token = token.substring(7);
        log.info("用户登出 - userId: {}, token: {}", userId, token);
        return userService.logout(userId, token);
    }

    /**
     * 获取用户信息
     *
     * @param id              用户ID
     * @param currentUserId   当前用户ID
     * @param currentUserType 当前用户类型
     * @return 用户信息
     */
    @ApiOperation("获取用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "currentUserId", value = "当前用户ID", required = true, dataType = "Long", paramType = "header"),
            @ApiImplicitParam(name = "currentUserType", value = "当前用户类型", required = true, dataType = "String", paramType = "header")
    })
    @GetMapping("/{id}")
    public BaseResponse<UserInfoResponse> getUserInfo(
            @ApiParam(value = "用户ID", required = true, example = "1001") @PathVariable Long id,
            HttpServletRequest request) {
        Long currentUserId = (Long) request.getAttribute("currentUserId");
        String currentUserType = (String) request.getAttribute("currentUserType");
        return userService.getUserInfo(id, currentUserId, currentUserType);
    }

    /**
     * 分页查询用户列表
     *
     * @param request         查询请求
     * @param currentUserId   当前用户ID
     * @param currentUserType 当前用户类型
     * @param httpRequest     HTTP请求，用于获取Authorization头
     * @return 用户列表
     */
    @ApiOperation("分页查询用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserId", value = "当前用户ID", required = true, dataType = "Long", paramType = "header"),
            @ApiImplicitParam(name = "currentUserType", value = "当前用户类型", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "Authorization", value = "Bearer token", required = true, dataType = "String", paramType = "header", example = "Bearer eyJhbGciOiJIUzI1NiJ9...")
    })
    @GetMapping
    public BaseResponse<PageResult<UserInfoResponse>> queryUserList(
            UserQueryRequest request,
            @RequestHeader Long currentUserId,
            @RequestHeader String currentUserType,
            HttpServletRequest httpRequest) {
        // 从Authorization头获取token
        String token = httpRequest.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
        }

        request.setCurrentUserId(currentUserId);
        request.setCurrentUserType(UserType.valueOf(currentUserType));
        return userService.queryUserList(request);
    }

    /**
     * 禁用用户
     *
     * @param id              用户ID
     * @param currentUserId   当前用户ID
     * @param currentUserType 当前用户类型
     * @return 操作结果
     */
    @ApiOperation("禁用用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "currentUserId", value = "当前用户ID", required = true, dataType = "Long", paramType = "header"),
            @ApiImplicitParam(name = "currentUserType", value = "当前用户类型", required = true, dataType = "String", paramType = "header")
    })
    @PutMapping("/{id}/disable")
    public BaseResponse<Void> disableUser(
            @PathVariable Long id,
            HttpServletRequest request) {
        Long currentUserId = (Long) request.getAttribute("currentUserId");
        String currentUserType = (String) request.getAttribute("currentUserType");
        return userService.disableUser(id, currentUserId, currentUserType);
    }

    /**
     * 启用用户
     *
     * @param id              用户ID
     * @param currentUserId   当前用户ID
     * @param currentUserType 当前用户类型
     * @return 操作结果
     */
    @ApiOperation("启用用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "currentUserId", value = "当前用户ID", required = true, dataType = "Long", paramType = "header"),
            @ApiImplicitParam(name = "currentUserType", value = "当前用户类型", required = true, dataType = "String", paramType = "header")
    })
    @PutMapping("/{id}/enable")
    public BaseResponse<Void> enableUser(
            @PathVariable Long id,
            HttpServletRequest request) {
        Long currentUserId = (Long) request.getAttribute("currentUserId");
        String currentUserType = (String) request.getAttribute("currentUserType");
        return userService.enableUser(id, currentUserId, currentUserType);
    }

    /**
     * 删除用户
     *
     * @param id              用户ID
     * @param currentUserId   当前用户ID
     * @param currentUserType 当前用户类型
     * @return 操作结果
     */
    @ApiOperation("删除用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "currentUserId", value = "当前用户ID", required = true, dataType = "Long", paramType = "header"),
            @ApiImplicitParam(name = "currentUserType", value = "当前用户类型", required = true, dataType = "String", paramType = "header")
    })
    @DeleteMapping("/{id}")
    public BaseResponse<Void> deleteUser(
            @PathVariable Long id,
            HttpServletRequest request) {
        Long currentUserId = (Long) request.getAttribute("currentUserId");
        String currentUserType = (String) request.getAttribute("currentUserType");
        return userService.deleteUser(id, currentUserId, currentUserType);
    }
} 